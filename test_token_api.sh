#!/bin/bash

echo "测试Token统计API功能"
echo "====================="

# 等待服务器完全启动
sleep 2

echo "测试API接口..."

# 测试管理员统计接口
echo "1. 测试管理员统计接口 /api/log/stat"
curl -s "http://localhost:3000/api/log/stat?type=2&start_timestamp=0&end_timestamp=9999999999" \
  -H "Content-Type: application/json" | python3 -m json.tool

echo ""

# 测试用户统计接口
echo "2. 测试用户统计接口 /api/log/self/stat"
curl -s "http://localhost:3000/api/log/self/stat?type=2&start_timestamp=0&end_timestamp=9999999999" \
  -H "Content-Type: application/json" | python3 -m json.tool

echo ""
echo "测试完成"
